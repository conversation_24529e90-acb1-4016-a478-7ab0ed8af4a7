export enum CodeErrorEnumException {
  //Usato se non si riesce a calcolare il tipo sentenza dalla penale_sentenza
  CALCOLO_TIPO_SENT_ERROR,
  //USATO per dire che un provvedimento non è stato CREATO
  PROVV_NOT_CREATED,
  // usato per indificare che non è stato provato un fascicolo o ricordo
  FASCICOLO_NOT_FOUND,
  //usato quando si verifica un errore nel firma e deposita
  FIRMA_DEP_ERROR,
  //usato quando si verifica un errore nel firma del provvediemnto tramite servizi depositi
  FIRMA_ERROR,
  //Usato quando un magistrato non è stato inserito nel sic penale
  MAGI_SIC_NOT_FOUND,
  //Usato quando il provvedimento nn viene trovato
  PROVV_NOT_FOUND,
  //Usato se si verifica qualche errore nella communicazione con servizi deposito
  SERVIZI_DEPOSITO_ERROR,
  //Usato quando non è stato inserito l'utente nel sic
  CODE_UTENTE_SIC,
  // usato nell'autentificazione e nel auth guard per segnale che il token e scaduto
  CODE_TOKEN_EXPIRED,
  //usato nell'autentificazione e nel auth guard per segnale che il token non è valido
  CODE_UNAUTHORIZED,
  //USATO per segnalere un errore nella richiesta di modifica
  RICHIESTA_MODIFICA,
  //VIene usato quando si verifica un errore nel duplica provvedimento
  DUPLICA_PROVV,
  //USATo per le credenziali errate
  ENDPOINT_OR_CREDENZIALI_ERRATE,
  // usato per sollevare l'errore che il provvedimento non puo essere duplicato perche c'è un altro in bozza
  PROVV_NOT_DUPLICATED,
  //USATO quando abbiamo un errore di firma nelle credenziali
  CREDENZIALI_ERRATE,
  // Usato quando ci viene passato un ruolo non gestito
  ROLE_NOT_MANAGED,
  // Usato quando servizi deposito restituisce un idCat null
  ID_DEPOSITO_NULL,
  //Usato quando il provvedimento è stato gia firmato e depositato
  PROVV_GIA_FIRMATO_DEPOSITATO,
  // usando quando il provvedimento viene gia lavorato da un altro utente
  PROVV_LOCKED,
  //usato per indicare che il provvedimento risulta essere già stato lavorato
  PROVV_LAVORATO,
  // usato quando si presenta un errore durante la firma: per esempio, le credenziali errate; è legato a CREDENZIALI_ERRATE
  DEPOSITO_INTERNAL_ERROR,
  // usato quando si ha un tipo di provvedimento non lo gestiamo
  NO_MANAGE_TIPO_PROVVEDIMENTO,
  // usato quando ci sono dei errori nel collegio
  COLLEGIO_ERROR,
  // usato quando si verifica un errore nel calcolo del numero del fascicolo o raccolta generale
  CALCOLO_NUMERO_FASCICOLO_ERROR,
  // usato quando non viene trovato l'ufficio nel DB
  UFFICIO_NOT_FOUND,
  // usato quando non viene trovato l'ufficio nel db
  UDIENZA_NOT_FOUND,
  // usato quando non si riesce a scaricare l'atto da servizi depositi
  ATTI_DOWNLOAD_ERROR,
  // usato quando non viene trovato il ricorso nel db
  RICORSO_NOT_FOUND,
  //usato quando nrg reale non è stato valorizzato
  NRG_REALE_NOT_VALORIZED,
  //usato quando i file xml non vengono trovati sul db
  FILE_NOT_FOUND,
  // usato quando il provvedimento nn  viene trovato nella cosa deposito
  CODA_DEPOSITO_NOT_FOUND,
  // usato quando si sta cercando di inserire nella coda di deposito un provvedimento che è già stato inserito
  CODA_DEPOSITO_ALREADY_EXIST,
  // usato per i campi obbligatori
  FIELD_MANDATORY,
  // USATO se il testo non è stato oscurato
  OSCURAMENTO_MANDATORY,
  //Usato quando non esiste il file pubblicato
  FILE_PUBBLICATO_NOT_FOUND,
  // usato quando non si è abilitati alla firma remota
  FIRMA_REMOTA_NOT_ENABLED,
  // usato quando il ruolo dell'utente non è definito
  RUOLO_NOT_DEFINED,
  // usato quando il cf del magistrato non esiste nel Sic
  CODE_MAGIS_SIC,
  //USATO per dire che un provvedimento non può essere eliminato
  PROVV_NOT_DELETED,
  //USATO per dire che non gestiamo questo tipo di sentenza
  TIPO_SENT_NOT_MANAGER,
  //USATO per dire che si è verificato un errore nell'udienza
  UDIENZA_ERROR,
  //USATO per indicare che non è stato trovato un ricorsoUdienza
  RICORSO_UDIENZA_NOT_FOUND,
  //USATO per indicare che il token jwt per la firma remota non valido o non corretto
  INVALID_JWT_TOKEN_SIGN_ERROR,
  //USATO per indicare lo username inserito per la firma remotanon è corretto
  USER_NOT_PRESENT_SIGN_ERROR,
  //USATO per indicare che la password di firma inserita non è corretta
  INCORRECT_PASSWORD_SIGN_ERROR,
  //USATO per indicare che il PIN di firma inserita non è corretta
  INCORRECT_PIN_SIGN_ERROR,
  //USATO per indicare che l'utente di firma remota risulta bloccato
  USER_BLOCKED_SIGN_ERROR,
  //USATO per indicare che il PDF è corrotto durante la firma (codice 134)
  PDF_CORRUPTED_SIGN_ERROR,
}

export enum NSTypeErrorEnum {
  ERROR = 'error',
  WARN = 'warning',
  SUCCESS = 'success',
  INFO = 'info',
}
