import { UfficioService } from '../multi-tenant/ufficio-service.decorator';
import { Inject, Logger, NotFoundException } from '@nestjs/common';
import { AuthService } from '../auth/auth.service';
import { ProvvedimentoChangeStatusService } from '../provvedimento-change-status/provvedimento-change-status.service';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { Utils } from '../utils/utils';
import { GenericErrorException } from '../exceptions/generic-error.exception';
import { CodeErrorEnumException } from '../exceptions/code-error-enum.exception';
import { ProvvedimentiService } from './provvedimenti.service';
import { ProvvedimentoRelazioneService } from '../provvedimento-change-status/provvedimento-relazione.service';
import { PenaleProvvedimentiEntity } from '../consultazioni-graphql/entities/penale_provvedimenti.entity';
import { CodeDepositoService } from '../code-deposito/code-deposito.service';
import { CodeDepositoInput } from '../consultazioni-graphql/entities/dto/code-deposito.input';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { DataSource, EntityManager } from 'typeorm';
import { CreateProvvedimentiChangeStatusInput } from '../consultazioni-graphql/entities/dto/create-provvedimenti-change-status.input';
import { PenaleProvvEditor } from '../consultazioni-graphql/models/penale_provv_editor.model';
import { ProvvEditorLavorazioneService } from './provvEditorLavorazione.service';
import { TRicorsoUdienzaService } from '../ricorso-udienza/t-ricorso-udienza.service';
import { RicercaSentenzaService } from '../ricerca-sentenza/ricerca-sentenza.service';

@UfficioService()
export class ProvvedimentoFindService {
  private logger = new Logger(ProvvedimentoFindService.name);

  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource,
    private readonly provvedimentiService: ProvvedimentiService,
    private readonly authService: AuthService,
    private readonly codeDepositoService: CodeDepositoService,
    private readonly changeStatusService: ProvvedimentoChangeStatusService,
    private readonly provvedimentoRelazioneService: ProvvedimentoRelazioneService,
    private readonly provvEditorLavorazioneService: ProvvEditorLavorazioneService,
    private readonly ricorsoUdienzaService: TRicorsoUdienzaService,
    private readonly ricercaSentenzaService: RicercaSentenzaService,
  ) {}

  //TODO: aggiungere il ruolo
  async provvedimentoFindByIdUdinAndNrg(idUdien: number, nrg: number) {
    if (!idUdien || !nrg) return [];
    this.logger.log(
      `Graphql query provvedimentoByIdUdienAndNrg. idUdien: ${idUdien},  nrg:${nrg}`,
    );
    const currentId = await this.authService.getCurrentId();
    const provvedimento =
      await this.provvedimentiService.provvedimentoByIdUdienAndNrgAndAutore(
        idUdien,
        nrg,
        currentId,
      );

    await this.checkStatoAndDuplicateProvvedimento(provvedimento);

    if (!provvedimento) {
      this.logger.warn(
        `Provvedimento non trovato. idUdien: ${idUdien}, nrg:${nrg}`,
      );
      throw new GenericErrorException(
        `Provvedimento non trovato. idUdien: ${idUdien}, nrg:${nrg}`,
        CodeErrorEnumException.PROVV_NOT_CREATED,
      );
    }
    return provvedimento;
  }

  async provvedimentoPresidenteFindByIdUdinAndNrg(
    idUdien: number,
    nrg: number,
  ) {
    this.logger.log(
      `Graphql query provvedimentoByNrgPerPresidente. nrg:${nrg}`,
    );
    let provvedimento =
      await this.provvedimentiService.provvedimentoByIdUdienAndNrgPerPresidente(
        idUdien,
        nrg,
      );
    provvedimento = provvedimento.filter(
      pro =>
        !(
          pro.stato === ProvvedimentiStatoEnum.BUSTA_RIFIUTATA &&
          Utils.isMinuta(pro.tipo)
        ),
    );
    for (let penaleProvvedimentiEntity of provvedimento) {
      penaleProvvedimentiEntity =
        await this.checkShowButtonVerificaAndModificaNew(
          penaleProvvedimentiEntity,
          false, // includeStatoCorrection = false per mantenere il comportamento originale del service
        );
    }
    if (!provvedimento) {
      this.logger.warn(
        `Provvedimento non trovato. nrg:${nrg}; idUdienza: ${idUdien}`,
      );
      throw new NotFoundException(nrg);
    }
    return provvedimento;
  }

  /**
   * Metodo centralizzato per calcolare enabledRichiestaDiModificaEVerificato
   * Unifica la logica precedentemente duplicata tra resolver e service
   * @param penaleProvvedimentiEntity - L'entità del provvedimento da processare
   * @param includeStatoCorrection - Se true, include la correzione dello stato tramite ricorsoUdienza (default: false)
   * @returns L'entità aggiornata con enabledRichiestaDiModificaEVerificato calcolato
   */
  public async checkShowButtonVerificaAndModificaNew(
    penaleProvvedimentiEntity: PenaleProvvedimentiEntity,
    includeStatoCorrection: boolean = false,
  ) {
    this.logger.log(
      `check se devo mostrare il bottone verifica e modifica. idProvv:${penaleProvvedimentiEntity?.idProvvedimento}`,
    );

    // Controllo preliminare: se il provvedimento è già stato duplicato, disabilita i bottoni
    // Questa è la stessa logica usata in checkOperation per generare l'errore 433
    const provvedimentoClonato =
      await this.provvedimentoRelazioneService.provvRelazioneByProvvOrigine(
        penaleProvvedimentiEntity.idProvvedimento,
      );
    if (provvedimentoClonato?.length > 0) {
      this.logger.log(
        `Provvedimento già duplicato, disabilito i bottoni. idProvv:${penaleProvvedimentiEntity.idProvvedimento}`,
      );
      penaleProvvedimentiEntity.enabledRichiestaDiModificaEVerificato = false;
      return penaleProvvedimentiEntity;
    }

    // Controllo preliminare: se il provvedimento è in stato MINUTA_DA_MODIFICARE, disabilita i bottoni
    // Questa è la stessa logica usata in checkOperation per generare l'errore 433
    if (penaleProvvedimentiEntity.stato === ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE) {
      this.logger.log(
        `Provvedimento in stato MINUTA_DA_MODIFICARE, disabilito i bottoni. idProvv:${penaleProvvedimentiEntity.idProvvedimento}`,
      );
      penaleProvvedimentiEntity.enabledRichiestaDiModificaEVerificato = false;
      return penaleProvvedimentiEntity;
    }

    // Correzione dello stato se richiesta (logica dal resolver)
    if (includeStatoCorrection) {
      try {
        const ricorsoUdienza =
          await this.ricorsoUdienzaService.ricorsoUdienzaByNrgAndIdUdienza(
            penaleProvvedimentiEntity.idUdienza,
            penaleProvvedimentiEntity.nrg,
          );

        if (ricorsoUdienza) {
          const statoRicorsoUdienza =
            await this.ricercaSentenzaService.sentenzaByIdRicUdien(
              ricorsoUdienza.idRicudien,
            );
          const statoCorretto = Utils.checkStatoCorretto(
            statoRicorsoUdienza,
            penaleProvvedimentiEntity.stato,
          );

          if (statoCorretto) {
            penaleProvvedimentiEntity.stato = statoCorretto;
          }
        } else {
          this.logger.warn(
            `Ricorso udienza non trovato per idUdienza: ${penaleProvvedimentiEntity.idUdienza} e nrg: ${penaleProvvedimentiEntity.nrg}`,
          );
        }
      } catch (error) {
        this.logger.error('Errore nel recupero di ricorsoUdienza', error);
      }
    }

    // prendo l'ultima relazione per l'ordinamento e ordinanza di questo provvedimento
    const provvedimentoPresidente =
      await this.provvedimentoRelazioneService.provvRelazioneByProvvDestLastDate(
        penaleProvvedimentiEntity.idProvvedimento,
      );

    // mi controllo il provvedimento orininale
    if (provvedimentoPresidente?.idProvvedimentoOrigine) {
      // vado a prendermi l'ultima relazione con questa origine
      const provveidmentoRelatore =
        await this.provvedimentoRelazioneService.provvRelazioneByProvvOrigineLastDate(
          penaleProvvedimentiEntity.idProvvedimento,
        );

      // controllo se la realzione esiste e vedo che non sia la stessa
      if (
        provveidmentoRelatore?.idProvvedimentoDestinazione &&
        provveidmentoRelatore.idProvvedimentoDestinazione ===
          provvedimentoPresidente.idProvvedimentoDestinazione
      ) {
        // significa che non è stato mai  duplicato
        // se è una sentenza o ordinanza e si trova nello stato rifiutata o minuta accetta allora i bottoni sono visibili
        penaleProvvedimentiEntity.enabledRichiestaDiModificaEVerificato =
          Utils.isOrdinanzaOSentenza(penaleProvvedimentiEntity.tipo) &&
          Utils.isDuplicabile(penaleProvvedimentiEntity?.stato);
      } else {
        // significa che è  stato duplicato da capire cosa fare se arriva busta rifiutata
        penaleProvvedimentiEntity.enabledRichiestaDiModificaEVerificato =
          Utils.isVerificabile(penaleProvvedimentiEntity.stato);
      }

      const provvedimentoPresidentePerMinute =
        await this.provvedimentoRelazioneService.provvRelazioneByProvvOrigine(
          penaleProvvedimentiEntity.idProvvedimento,
        );
      const provvedimentoMinutaOriginale =
        await this.provvedimentoRelazioneService.provvRelazioneByProvvDest(
          penaleProvvedimentiEntity.idProvvedimento,
        );
      let provvOriginale = null;
      if (provvedimentoMinutaOriginale?.length > 0) {
        provvOriginale = await this.provvedimentiService.provvedimentoById(
          provvedimentoMinutaOriginale[0].idProvvedimentoOrigine,
        );
      }

      // se lo stato è minuta accetta e si tratta di un minuta e non ha duplicati allora si può abilitare il bottone
      // Logica unificata: include sia MINUTA_ACCETTATA che BUSTA_RIFIUTATA (dal service originale)
      if (
        (penaleProvvedimentiEntity.stato ==
          ProvvedimentiStatoEnum.MINUTA_ACCETTATA ||
          penaleProvvedimentiEntity.stato ==
            ProvvedimentiStatoEnum.BUSTA_RIFIUTATA) &&
        provvedimentoPresidentePerMinute?.length > 0
      ) {
        for (const penaleMPProvvEntity of provvedimentoPresidentePerMinute) {
          const provvDaControllare =
            await this.provvedimentiService.provvedimentoById(
              penaleMPProvvEntity.idProvvedimentoDestinazione,
            );
          if (provvDaControllare && Utils.isMinuta(provvDaControllare?.tipo)) {
            penaleProvvedimentiEntity.disabledButton = true;
          }
          if (
            provvDaControllare &&
            Utils.isOrdinanzaOSentenza(provvDaControllare?.tipo)
          ) {
            penaleProvvedimentiEntity.enabledRichiestaDiModificaEVerificato =
              false;
          }
        }
      }

      // Controllo specifico per BUSTA_RIFIUTATA con MINUTA_DA_MODIFICARE (dal service originale)
      if (
        penaleProvvedimentiEntity.stato ==
          ProvvedimentiStatoEnum.BUSTA_RIFIUTATA &&
        provvOriginale?.stato == ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE
      ) {
        penaleProvvedimentiEntity.enabledRichiestaDiModificaEVerificato = false;
      } else {
        penaleProvvedimentiEntity.enabledRichiestaDiModificaEVerificato =
          Utils.isVerificabile(penaleProvvedimentiEntity.stato) &&
          !provveidmentoRelatore;
      }
    } else {
      //caso in cui è una MINUTA_ACCETTATA ma è stata già modificata ed inviata all'estensore
      penaleProvvedimentiEntity.enabledRichiestaDiModificaEVerificato =
        await this.isMinutaAccettataAndInviataAdEstensore(
          penaleProvvedimentiEntity,
        );
    }

    return penaleProvvedimentiEntity;
  }

  private async isMinutaAccettataAndInviataAdEstensore(
    penaleProvvedimentiEntity: PenaleProvvedimentiEntity,
  ) {
    const minutaAccettataEInviataAdEstensore =
      await this.provvedimentoRelazioneService.provvRelazioneByProvvOrigine(
        penaleProvvedimentiEntity.idProvvedimento,
      );
    if (minutaAccettataEInviataAdEstensore?.length > 0) {
      for (const penaleMPProvvEntity of minutaAccettataEInviataAdEstensore) {
        const provvDaControllare =
          await this.provvedimentiService.provvedimentoById(
            penaleMPProvvEntity.idProvvedimentoDestinazione,
          );
        if (provvDaControllare && Utils.isMinuta(provvDaControllare?.tipo)) {
          return false;
        }
      }
    }
    return Utils.isVerificabile(penaleProvvedimentiEntity.stato);
  }

  private async checkStatoAndDuplicateProvvedimento(
    provvedimento: PenaleProvvedimentiEntity[],
  ) {
    for (const penaleProvvedimentiEntity of provvedimento) {
      // prendo l'ultimo relazione inserita che è quella che conta
      const provvedimentoRelatore =
        await this.provvedimentoRelazioneService.provvRelazioneByProvvOrigineLastDate(
          penaleProvvedimentiEntity.idProvvedimento,
        );
      if (provvedimentoRelatore?.idProvvedimentoDestinazione) {
        const lastState =
          await this.changeStatusService.changeStatusByIdProvvAndLast(
            provvedimentoRelatore.idProvvedimentoDestinazione,
          );
        if (
          lastState?.stato &&
          lastState?.stato != ProvvedimentiStatoEnum.CODA_DI_FIRMA
        ) {
          if (
            lastState?.stato &&
            penaleProvvedimentiEntity.stato ==
              ProvvedimentiStatoEnum.MINUTA_ACCETTATA &&
            Utils.isOrdinanzaOSentenza(penaleProvvedimentiEntity.tipo) &&
            Utils.isStatoDelPresidente(lastState?.stato)
          ) {
            penaleProvvedimentiEntity.disabledButton = true;
          }
        }
      }
      await this.checkDuplicabeProvv(provvedimento, penaleProvvedimentiEntity);
    }
  }

  async provvedimentoFindByNrg(nrg: number) {
    if (!nrg) return [];
    this.logger.log(`Graphql query provvedimentoByNrg. nrg:${nrg}`);
    const currentId = await this.authService.getCurrentId();
    const provvedimento =
      await this.provvedimentiService.provvedimentoByNrgAndAutore(
        nrg,
        currentId,
      );
    await this.checkStatoAndDuplicateProvvedimento(provvedimento);

    if (!provvedimento) {
      this.logger.warn(`Provvedimento non trovato. nrg:${nrg}`);
      throw new GenericErrorException(
        `Provvedimento non trovato. nrg:${nrg}`,
        CodeErrorEnumException.PROVV_NOT_CREATED,
      );
    }
    return provvedimento;
  }

  private async checkDuplicabeProvv(
    provvedimento: PenaleProvvedimentiEntity[],
    penaleProvvedimentiEntity: PenaleProvvedimentiEntity,
  ) {
    this.logger.log(
      `Check se è un provvedimento duplicato. idProvv:${penaleProvvedimentiEntity?.idProvvedimento}`,
    );
    if (provvedimento) {
      const provvedimentoPresidente =
        await this.provvedimentoRelazioneService.provvRelazioneByProvvDestLastDate(
          penaleProvvedimentiEntity.idProvvedimento,
        );
      // controllo il provvedimento originale
      if (provvedimentoPresidente?.idProvvedimentoOrigine) {
        // vado a prendermi l'ultima relazione con questa origine
        const provvedimentoRelatore =
          await this.provvedimentoRelazioneService.provvRelazioneByProvvOrigineLastDate(
            penaleProvvedimentiEntity.idProvvedimento,
          );
        // controllo se la relazione esiste e vedo che non sia la stessa

        penaleProvvedimentiEntity.isDuplicato = !(
          provvedimentoRelatore?.idProvvedimentoDestinazione &&
          provvedimentoRelatore.idProvvedimentoDestinazione ===
            provvedimentoPresidente.idProvvedimentoDestinazione
        );
        penaleProvvedimentiEntity.hasDuplicato =
          !!provvedimentoRelatore?.idProvvedimentoOrigine;
      } else {
        penaleProvvedimentiEntity.isDuplicato = false;

        const provvedimentoRelatore =
          await this.provvedimentoRelazioneService.provvRelazioneByProvvOrigineLastDate(
            penaleProvvedimentiEntity.idProvvedimento,
          );
        penaleProvvedimentiEntity.hasDuplicato =
          !!provvedimentoRelatore?.idProvvedimentoOrigine;
      }
    }
  }

  /* Service to add into Coda Deposito directly */
  async addCodaDeposito(idProvv: string) {
    try {
      return await this.connection.manager.transaction(
        async (entityManager: EntityManager) => {
          const currentUser = await this.authService.getCurrentUser();
          const currentUserId = await this.authService.getCurrentId();
          const codeFirmaRel =
            await this.codeDepositoService.codeDepositoByIdProvvCf(
              idProvv,
              currentUser,
            );
          if (codeFirmaRel) {
            this.logger.log(
              `Provvedimento già presente nella coda firma. idProvv:${idProvv}`,
            );
            throw new GenericErrorException(
              'Provvedimento già presente nella coda firma',
              CodeErrorEnumException.CODA_DEPOSITO_ALREADY_EXIST,
            );
          }

          const provv =
            await this.provvedimentiService.getProvvedimentoByIdProvvedimento(
              idProvv,
            );
          if (idProvv && provv?.stato) {
            const changeStatusValue =
              new CreateProvvedimentiChangeStatusInput();
            changeStatusValue.idProvvedimento = provv.idProvvedimento;
            changeStatusValue.stato = ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL;
            changeStatusValue.idAutore = currentUserId;
            changeStatusValue.prevStato = provv.stato;
            provv.dataUltimaModifica = new Date();

            provv.stato = ProvvedimentiStatoEnum.IN_CODE_FIRMA_REL;
            await this.changeStatusService.createProvvedimentoChangeStatus(
              changeStatusValue,
              entityManager,
            );
            this.logger.log(
              `Aggiorno lo stato del provvedimento in coda di firma. idProvv: ${idProvv}`,
            );
            const updateResult =
              await this.provvedimentiService.updateStatoProvvedimento(
                idProvv,
                provv.stato,
                entityManager,
              );

            const codaInput = new CodeDepositoInput();
            codaInput.idProvv = idProvv;
            codaInput.cf = currentUser;
            await this.codeDepositoService.createCodeDeposito(codaInput);
            this.logger.log(
              `Provvedimento verificato. idProvv: ${idProvv}, updateResultRowNumber:${updateResult?.affected}`,
            );
            return provv;
          }
          throw new GenericErrorException(
            'Provvedimento non trovato ',
            CodeErrorEnumException.PROVV_NOT_FOUND,
          );
        },
      );
    } catch (err) {
      this.logger.error(`Provvedimento non trovato. idProvv: ${idProvv}`, err);
      throw err;
    }
  }

  async getEditorOnlineTesti(
    idProvv: string,
    idUdienza: number,
    nrg: number,
    currentCf: string,
  ) {
    const penaleProvvEditorEntity =
      await this.provvEditorLavorazioneService.getProvvedimentoEditorByIdProvv(
        idProvv,
      );
    if (penaleProvvEditorEntity) {
      // semplificata, idSezionale, tipoProvvedimento
      const tipoAndSemplificataProvv = await this.getTipoAndSemplificataProvv(
        idUdienza,
        nrg,
        currentCf,
      );
      const [anno, numero] = Utils.calcolaIdSezionale(
        tipoAndSemplificataProvv.sentenza + '',
      );
      let sezionale = null;
      if (numero && anno) {
        sezionale = numero + '/' + anno;
      }
      const penaleProvvEditor = new PenaleProvvEditor({
        ...penaleProvvEditorEntity,
        semplificata: tipoAndSemplificataProvv.semplificata,
        tipoProvvedimento: tipoAndSemplificataProvv.tipoProvvedimento,
        idSezionale: sezionale,
      });
      return penaleProvvEditor;
    }
    return null;
  }

  async getTipoAndSemplificataProvv(
    idUdienza: number,
    nrg: number,
    currentUserCf: string,
  ) {
    const semplificataETipoProvv =
      await this.provvedimentiService.getTipoProvvedimentoAndSemplificataByNrg(
        idUdienza,
        nrg,
      );
    semplificataETipoProvv.tipoProvvedimento =
      await this.provvedimentiService.checkTipoDiProvvedimento(
        semplificataETipoProvv?.tipoProvvedimento,
        idUdienza,
        semplificataETipoProvv.idEstensore,
        currentUserCf,
      );
    return semplificataETipoProvv;
  }
}
