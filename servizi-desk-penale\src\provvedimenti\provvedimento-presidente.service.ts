import { Inject, Logger, StreamableFile } from '@nestjs/common';
import { UfficioService } from 'src/multi-tenant/ufficio-service.decorator';
import { ProvvLavorazioneService } from './provvLavorazione.service';
import { CreateProvvedimentiChangeStatusInput } from '../consultazioni-graphql/entities/dto/create-provvedimenti-change-status.input';
import { ProvvedimentiStatoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-stato.enum';
import { ProvvedimentoChangeStatusService } from '../provvedimento-change-status/provvedimento-change-status.service';
import { AuthService } from '../auth/auth.service';
import { PenaleProvvedimentiEntity } from '../consultazioni-graphql/entities/penale_provvedimenti.entity';
import { ProvvedimentiNoteService } from '../provvedimenti-note/provvedimenti-note.service';
import { CreateProvvedimentiNoteInput } from '../consultazioni-graphql/entities/dto/create-provvedimenti-note.input';
import { UFFICIO_CONNECTION } from '../multi-tenant/multi-tenant.module';
import { DataSource, EntityManager } from 'typeorm';
import { ProvvedimentiTipoEnum } from '../consultazioni-graphql/entities/enumaration/provvedimenti-tipo.enum';
import { ProvvedimentiService } from './provvedimenti.service';
import { CodeDepositoService } from '../code-deposito/code-deposito.service';
import { CodeDepositoInput } from '../consultazioni-graphql/entities/dto/code-deposito.input';
import {
  DownloadCodaDepositoInput,
  FirmaPresidenteInput,
  FirmaProvvLavorazioneInput,
} from './entities/dto/firma-provvLavorazione.input';
import { PenaleProvvLavFileEntity } from '../consultazioni-graphql/entities/penale_provv_lav_file.entity';
import { CreateProvvLavorazioneInput } from '../consultazioni-graphql/entities/dto/create-provvLavorazione.input';
import { ArgsDepositoProvvedimentoInput } from './entities/dto/provvLavorazione.input';
import { TRicorsoService } from '../fascicolo/t-ricorso.service';
import { Utils } from '../utils/utils';
import { UfficiDBService } from '../uffici/ufficiDB.service';
import { NotificheService } from '../notifiche/notifiche.service';
import { CreateNotificheInput } from '../consultazioni-graphql/entities/dto/create-notifiche.input';
import { NotificaTipoEnum } from '../consultazioni-graphql/entities/enumaration/notifica-tipo.enum';
import { ProvvedimentoRelazioneService } from '../provvedimento-change-status/provvedimento-relazione.service';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import * as JSZip from 'jszip';
import {
  CreateHtmlIndexService,
  FascicoloInfo,
  PilotInfo,
  UdienzaInfo,
} from './create-html.index.service';
import { UdienzaService } from 'src/udienza/udienza.service';
import { ProvvedimentoNotFoundException } from '../exceptions/provvedimento-not-found.exception';
import { RichiestaModificaException } from '../exceptions/richiesta-modifica.exception';
import { DuplicaProvvedimentoException } from '../exceptions/duplica-provvedimento.exception';
import { ServiziDepositoException } from '../exceptions/servizi-deposito.exception';
import { CodeErrorEnumException } from '../exceptions/code-error-enum.exception';
import { GenericErrorException } from '../exceptions/generic-error.exception';
import { PresidenteTipoModificaEnum } from './entities/dto/presidente-dto';
import { PenaleMPProvvEntity } from '../consultazioni-graphql/entities/penale_mp_provv.entity';
import { FirmaEDepositaException } from 'src/exceptions/firma-e-deposita.exception';
import { ProvvedimentoNonLavorabileException } from 'src/exceptions/provvedimento-non-lavorabile.exception';

@UfficioService()
export class ProvvedimentoPresidenteService {
  private logger = new Logger(ProvvedimentoPresidenteService.name);

  constructor(
    @Inject(UFFICIO_CONNECTION) private connection: DataSource,
    private configService: ConfigService,
    private readonly provvLavorazService: ProvvLavorazioneService,
    private readonly authService: AuthService,
    private readonly udienzaService: UdienzaService,
    private readonly provvedimentiService: ProvvedimentiService,
    private readonly depositoService: CodeDepositoService,
    private readonly ricorsoService: TRicorsoService,
    private readonly ufficiDBService: UfficiDBService,
    private readonly notificheService: NotificheService,
    private readonly provvedimentoNoteService: ProvvedimentiNoteService,
    private readonly provvedimentoRelazioneService: ProvvedimentoRelazioneService,
    private readonly createHtmlIndexService: CreateHtmlIndexService,
    private readonly changeStatusService: ProvvedimentoChangeStatusService,
  ) {}

  async getProvvLavorazioneByIdProvv(idProvv: string) {
    try {
      return await this.provvLavorazService.getProvvLavorazioneByIdProvvedimento(
        idProvv,
      );
    } catch (e) {
      this.logger.error(
        `Errore nella ricerca del provvedimento. idProvv:${idProvv}`,
      );
      throw new ProvvedimentoNotFoundException(
        'Errore nella riceca del provvedimento',
      );
    }
  }
  /**
   * Verifica l'operazione richiesta è consentita o meno, in funzione dello stato attuale del provvedimento
   * @param provv provvedimento da verificare
   * @param disableWarn se true non solleva l'eccezione con il messaggio di warning
   * @returns true se i controlli sono andati a buon fine, false o eccezione altrimenti
   */
  async checkOperation(
    provv: PenaleProvvedimentiEntity,
    disableWarn?: boolean,
  ): Promise<boolean> {
    if (provv) {
      const provvedimentoClonato =
        await this.provvedimentoRelazioneService.provvRelazioneByProvvOrigine(
          provv.idProvvedimento,
        );
      if (provvedimentoClonato?.length > 0) {
        if (disableWarn) {
          this.logger.log(
            `Caso di opreazione non consentita perchè il provvedimento risulta essere duplicato ma non segnalata all'utente. idProvv:${provv.idProvvedimento}`,
          );
          return false;
        }
        this.logger.warn(
          `Operazione non consentita perchè il provvedimento risulta essere duplicato. idProvv:${provv.idProvvedimento}`,
        );
        throw new ProvvedimentoNonLavorabileException(
          `Operazione non consentita perchè il provvedimento risulta essere duplicato`,
        );
      }
      if (provv.stato === ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE) {
        if (disableWarn) {
          this.logger.log(
            `Caso di opreazione non consentita perchè il provvedimento si trova in stato 'Minuta da modificare' ma non segnalata all'utente. idProvv:${provv.idProvvedimento}`,
          );
          return false;
        }
        this.logger.warn(
          `Operazione non consentita perchè il provvedimento si trova in stato 'Minuta da modificare'. idProvv:${provv.idProvvedimento}`,
        );
        throw new ProvvedimentoNonLavorabileException(
          `Operazione non consentita perchè il provvedimento si trova in stato 'Minuta da modificare'`,
        );
      }
    }
    return true;
  }

  /**
   *  richista di modifica o modifica presidnet edi una minuta quindi modifica diretta di una minuta
   *  non diventa mai sentenza o ordinaza
   * @param provv
   * @param idAutore
   * @param tipoModifica
   * @param noteString
   */
  async richiestaModifica(
    provv: PenaleProvvedimentiEntity,
    idAutore: number,
    tipoModifica: PresidenteTipoModificaEnum | undefined,
    noteString: string | null,
  ) {
    try {
      return await this.connection.manager.transaction(
        async (entityManager: EntityManager) => {
          this.logger.log(
            `Inizio transazione della richiesta di modifica per una minuta. provvId:${provv.idProvvedimento}, nrg: ${provv.nrg}, tipoProvv:${provv.tipo}`,
          );

          let stato = ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE;
          if (
            tipoModifica ===
            PresidenteTipoModificaEnum.MINUTA_MODIFICATA_PRESIDENTE
          ) {
            stato = ProvvedimentiStatoEnum.MINUTA_MODIFICATA_PRESIDENTE;
          }
          const tipoProv = this.getTipoProvvPerRelatore(provv.tipo);
          if (provv?.idProvvedimento) {
            await this.insertChangeStatusProvv(
              provv,
              idAutore,
              tipoModifica,
              entityManager,
            );
            await this.provvedimentiService.updateTipoAndStatusProvvedimento(
              provv.idProvvedimento,
              tipoProv,
              stato,
              entityManager,
            );
            await this.creaNotaSulProvvedimento(
              provv,
              noteString ?? '',
              idAutore,
              entityManager,
            );
            await this.creaNotificaPerIlRelatore(
              provv,
              noteString,
              stato === ProvvedimentiStatoEnum.MINUTA_MODIFICATA_PRESIDENTE,
            );
            this.logger.log(
              `Fine transazione della richiesta di modifica per una minuta.`,
            );
            return provv;
          }
        },
      );
    } catch (e) {
      this.logger.error('Errore nella richiesta di modifica', e);
      throw new RichiestaModificaException(
        'Errore nella richiesta di modifica. ',
        e,
      );
    }
  }

  async getProvvFiglioEstensoreForRichiestaModificaEModifica(
    currentProvv: PenaleProvvedimentiEntity,
  ) {
    if (currentProvv.stato == ProvvedimentiStatoEnum.MINUTA_ACCETTATA) {
      return currentProvv;
    }
    let idPRovvRicerca: string | undefined = currentProvv.idProvvedimento;
    let n = 0;
    while (n < 20 && idPRovvRicerca) {
      const provvFiglioRelazione: PenaleMPProvvEntity | null =
        await this.provvedimentoRelazioneService.provvRelazioneByProvvDestLastDate(
          idPRovvRicerca,
        );
      if (provvFiglioRelazione?.idProvvedimentoOrigine) {
        const provvFiglio: PenaleProvvedimentiEntity | null =
          await this.provvedimentiService.provvedimentoById(
            provvFiglioRelazione?.idProvvedimentoOrigine,
          );
        if (provvFiglio?.stato == ProvvedimentiStatoEnum.MINUTA_ACCETTATA) {
          return provvFiglio;
        }
        idPRovvRicerca = provvFiglio?.idProvvedimento;
      } else {
        throw new GenericErrorException(
          'Minuta accetta figlia non trovata ',
          CodeErrorEnumException.PROVV_NOT_DUPLICATED,
        );
      }
      n++;
    }
    throw new GenericErrorException(
      'Minuta accetta figlia non trovata ',
      CodeErrorEnumException.PROVV_NOT_DUPLICATED,
    );
  }

  /**
   *  Richiesta di modifica o modifica presidente di una ordinanza o sentenza
   *  caso in cui il presidente lo esegue su una busta rifiutata.
   * @param provv
   * @param idAutore
   * @param noteString
   */
  async richiestaModificaOrdinanzaOSentenza(
    provv: PenaleProvvedimentiEntity,
    idAutore: number,
    noteString: string,
  ) {
    try {
      return await this.connection.manager.transaction(
        async (entityManager: EntityManager) => {
          this.logger.log(
            `Inizio transazione della richiesta di modifica per una ordinaza/sentezza. idProvv:${provv.idProvvedimento}; nrg:${provv.nrg}; tipoProvv:${provv.tipo}`,
          );
          if (provv?.idProvvedimento) {
            // prelevo il figlio di questa ordinanza
            const provvFiglioEstensore =
              await this.getProvvFiglioEstensoreForRichiestaModificaEModifica(
                provv,
              );

            if (provvFiglioEstensore?.idProvvedimento) {
              // setto lo stato della minuta in da modificare
              await this.provvedimentiService.updateTipoAndStatusProvvedimento(
                provvFiglioEstensore.idProvvedimento,
                this.getTipoProvvPerRelatore(provvFiglioEstensore.tipo),
                ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE,
                entityManager,
              );
              // creo la nota del presidente per il relatore e gli aggiungo la nota
              await this.creaNotaSulProvvedimento(
                provvFiglioEstensore,
                noteString,
                idAutore,
                entityManager,
              );
              // creo la notifica per il relatore del provvedimento
              await this.creaNotificaPerIlRelatore(
                provvFiglioEstensore,
                noteString,
                provvFiglioEstensore.stato ===
                  ProvvedimentiStatoEnum.MINUTA_MODIFICATA_PRESIDENTE,
              );
              // inserisco solo il cambio stato per il provvedimento figlio e ignoro il provvedimento padre
              // altrimenti nel tracking compaiono 2 richieste di notifiche
              await this.insertChangeStatusProvv(
                provvFiglioEstensore,
                idAutore,
                PresidenteTipoModificaEnum.RICHIESTA_MODIFICA,
                entityManager,
                provv.stato,
              );
              this.logger.log(
                `Fine transazione della richiesta di modifica per una minuta.`,
              );
              return provv;
            }
            this.logger.warn(
              `Errore minuta figlia non trovata.  idProvv:${provv.idProvvedimento}; nrg:${provv.nrg}; tipoProvv:${provv.tipo}.`,
            );
            throw new ProvvedimentoNotFoundException(
              'Minuta figlia della sentenza or ordinanza non trovata',
            );
          }
          this.logger.warn(
            `Errore provvedimento non trovata o idProvvedimento non valorizzato.  idProvv:${provv.idProvvedimento}; nrg:${provv.nrg}; tipoProvv:${provv.tipo}.`,
          );
          throw new ProvvedimentoNotFoundException(
            'Id provvedimento non valorizzato',
          );
        },
      );
    } catch (e) {
      console.log(
        'Errore nella richiesta di modifica del presidente di una sentenza/ordinanza. provvedimento:',
        provv,
        e,
      );
      throw new RichiestaModificaException(
        'Errore nella richiesta di modifica del presidente di una sentenza/ordinanza. ',
        e,
      );
    }
  }

  getTipoProvvPerRelatore(tipo: ProvvedimentiTipoEnum) {
    switch (tipo) {
      case ProvvedimentiTipoEnum.ORDINANZA:
        return ProvvedimentiTipoEnum.ORDINANZA;
      case ProvvedimentiTipoEnum.SENTENZA:
        return ProvvedimentiTipoEnum.SENTENZA;
      case ProvvedimentiTipoEnum.MINUTA_ORDINANZA:
        return ProvvedimentiTipoEnum.MINUTA_ORDINANZA;
      case ProvvedimentiTipoEnum.MINUTA_SENTENZA:
        return ProvvedimentiTipoEnum.MINUTA_SENTENZA;
      default:
        return tipo;
    }
  }

  async aggiugiAllaCodaDiFirma(
    idProvvOld: string,
    cf: string,
    idAutore: number,
  ) {
    //ATTENZIONE non fare entityManager.release() lo gestisce la transazione manager con la callback
    return await this.connection.manager.transaction(
      async (entityManager: EntityManager) => {
        this.logger.log(
          `Aggiungi alla coda di firma transitional con idProvv:${idProvvOld} e idPresidente::${idAutore}`,
        );
        const penaleProvvedimentiEntityPromise =
          await this.duplicaFilePerVerificato(cf, idProvvOld, entityManager);
        const code = new CodeDepositoInput();

        if (penaleProvvedimentiEntityPromise.idProvvedimento) {
          this.logger.debug(
            `Creao la relazione tra i due provvedimenti dupplicati. idProvvDuplicato: ${penaleProvvedimentiEntityPromise?.idProvvedimento}, idprovvOld:${idProvvOld}`,
          );
          await this.provvedimentoRelazioneService.insertRelazioneTraProvv(
            penaleProvvedimentiEntityPromise.idProvvedimento,
            idProvvOld,
            entityManager,
          );
          code.idProvv = penaleProvvedimentiEntityPromise.idProvvedimento;
          code.cf = cf;
          const changeStatusValue = new CreateProvvedimentiChangeStatusInput();
          changeStatusValue.idProvvedimento =
            penaleProvvedimentiEntityPromise.idProvvedimento;
          changeStatusValue.stato = ProvvedimentiStatoEnum.CODA_DI_FIRMA;
          changeStatusValue.idAutore = idAutore;
          changeStatusValue.prevStato = penaleProvvedimentiEntityPromise.stato;
          penaleProvvedimentiEntityPromise.stato =
            ProvvedimentiStatoEnum.INVIATO_IN_CANCEL_PRESIDENTE;
          penaleProvvedimentiEntityPromise.dataUltimaModifica = new Date();
          this.logger.debug(
            `Aggiungo uno nuovo stato stato nella tracking dei provvedimenti per idProvv: ${
              penaleProvvedimentiEntityPromise?.idProvvedimento
            }, status:${JSON.stringify(changeStatusValue)}`,
          );
          await this.changeStatusService.createProvvedimentoChangeStatus(
            changeStatusValue,
            entityManager,
          );
          this.logger.debug(
            `aggiorno lo stato del provvedimento in coda di firma idProvv:${penaleProvvedimentiEntityPromise.idProvvedimento}`,
          );
          await this.provvedimentiService.updateStatoProvvedimento(
            penaleProvvedimentiEntityPromise.idProvvedimento,
            ProvvedimentiStatoEnum.CODA_DI_FIRMA,
            entityManager,
          );
          const codaDeposito = await this.depositoService.createCodeDeposito(
            code,
            entityManager,
          );
          return codaDeposito;
        }
        this.logger.warn(`Provvedimento non trovato. idProvv: ${idProvvOld}`);
        throw new GenericErrorException(
          'Id provvedimento non valorizzato',
          CodeErrorEnumException.PROVV_NOT_FOUND,
        );
      },
    );
  }

  getCodaDeposito(cf: string) {
    const promise = this.depositoService.codeDepositoByCf(cf);
    this.logger.log(`Restituisco la coda di deposito per l'utente loggato.`);
    return promise;
  }

  deleteProvvedimento(idProv: string, cf: string) {
    const depInput = new CodeDepositoInput();
    depInput.cf = cf;
    depInput.idProvv = idProv;
    this.provvLavorazService.deleteProvvLavorazione(idProv);
    const deleteCode = this.depositoService.deleteCodeDeposito(depInput);
    this.logger.log(
      `Cancello il provvedimento dalla coda di deposito e quello dupplicato con idProvv: ${idProv}`,
    );
    return deleteCode;
  }

  async duplicaFilePerVerificato(
    currentUser: string,
    idProvv: string,
    entityManager: EntityManager,
  ) {
    const ufficioDB = await this.ufficiDBService.getFirstUfficio();
    Utils.checkUfficoDB(ufficioDB);
    try {
      this.logger.log('Credo una copia del provvedimento. idprovv:', idProvv);
      const { provv, isPresidente, isEstensore } =
        await this.getProvvIsEstensoreIsPresidente(idProvv);
      const provvCopiato = await this.creaCopyDelProvvedimento(
        provv,
        isPresidente,
        isEstensore,
        entityManager,
        true,
      );
      if (provvCopiato?.idProvvedimento) {
        const provvLavorazione = new CreateProvvLavorazioneInput();
        provvLavorazione.nrg = provvCopiato.nrg;
        provvLavorazione.origine = provvCopiato.origine;
        provvLavorazione.idUdienza = provvCopiato.idUdienza;
        provvLavorazione.argsProvvedimento =
          new ArgsDepositoProvvedimentoInput();
        provvLavorazione.argsProvvedimento.tipologiaProvvedimento =
          provvCopiato.tipo;
        const ricorso = await this.ricorsoService.ricorsoFindByNrg(
          provvCopiato.nrg,
        );
        if (!ricorso?.nrgReale) {
          this.logger.warn(
            `Ricorso o nrgReale non trovato. nrg:${provvCopiato.nrg}`,
          );
          throw new GenericErrorException(
            'Ricorso o nrgReale non trovato',
            ricorso
              ? CodeErrorEnumException.RICORSO_NOT_FOUND
              : CodeErrorEnumException.NRG_REALE_NOT_VALORIZED,
          );
        }
        const [anno, numero] = Utils.calcoloNumeroFascicolo(
          ricorso.nrgReale.toString(),
        );
        if (!anno || !numero) {
          throw new GenericErrorException(
            'Anno e numero fascicolo non calcolato.',
            CodeErrorEnumException.CALCOLO_NUMERO_FASCICOLO_ERROR,
          );
        }
        provvLavorazione.argsProvvedimento.anRuolo = anno;
        provvLavorazione.argsProvvedimento.numRuolo = numero;
        const provvvvv = await this.getProvvLavorazioneByIdProvv(idProvv);
        await this.creaCopyDelProvvedimentoLavorazioneFileAndRigeneraDAtiAtto(
          idProvv,
          provvvvv ? provvvvv.fkIdCat! : 0,
          provvCopiato?.idProvvedimento,
          provvLavorazione,
          entityManager,
        );
        this.logger.log(`Provvedimento duplicato con idProvv:${idProvv}`);
        return provvCopiato;
      }
    } catch (e) {
      this.logger.error(
        `Si è verificato un errore per il duplica provvedimento con idProvv:${idProvv}`,
        e,
      );
      if (e instanceof ServiziDepositoException) {
        throw new ServiziDepositoException(e.message, e);
      }
      throw new DuplicaProvvedimentoException(
        'Si è verificato un errore per il duplica provvedimento',
        e,
      );
    }
    this.logger.error(
      `Si è verificato un errore per il duplica provvedimento con idProvv:${idProvv}`,
    );
    throw new DuplicaProvvedimentoException(
      'Si è verificato un errore per il duplica provvedimento',
    );
  }

  async downloadCodaDeposito(inputCodaDeposito: DownloadCodaDepositoInput) {
    const zip = new JSZip();
    const pilotInfos = new Array<PilotInfo>();
    // Tieni traccia di tutti i download schedulati
    const promises: Promise<unknown>[] = [];
    for (const fascicolo of inputCodaDeposito.daScaricare) {
      const datiUdienza = new UdienzaInfo();
      const fascicoliIfos = new Array<FascicoloInfo>();
      const allData = await this.provvLavorazService.getAllFileProvvedimento(
        fascicolo,
      );
      if (!allData) {
        this.logger.warn(`Nessun dato trovato per il fascicolo:${fascicolo}`);
        return;
      }
      // Filtra i file DOCX per essere coerente con downloadAtto che usa getProvvLavorazioneByIdCatUnlessDocx
      const data = allData.filter(
        file =>
          file.mimeType !==
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      );
      if (!data || data.length === 0) {
        this.logger.warn(
          `Nessun file valido trovato dopo il filtro per il fascicolo:${fascicolo}`,
        );
        continue;
      }
      let folderFascicolo = null;
      const provv = await this.provvedimentiService.provvedimentoById(
        data[0].idProvvedimento,
      );

      datiUdienza.nrg = provv!.nrg;
      const ricorso = await this.ricorsoService.ricorsoFindByNrg(provv!.nrg);
      if (!ricorso?.nrgReale) {
        this.logger.warn(`Ricorso o nrgReale non trovato. nrg:${provv!.nrg}`);
        throw new GenericErrorException(
          'Ricorso o nrgReale non trovato',
          ricorso
            ? CodeErrorEnumException.RICORSO_NOT_FOUND
            : CodeErrorEnumException.NRG_REALE_NOT_VALORIZED,
        );
      }
      const [anno, numero] = Utils.calcoloNumeroFascicolo(
        ricorso.nrgReale.toString(),
      );

      datiUdienza.anno = anno ?? 0;
      datiUdienza.numero = numero ?? 0;
      datiUdienza.nrg = ricorso.nrg;
      try {
        if (provv?.idUdienza) {
          const udienzaPerPresidenteByIdUdienza =
            await this.udienzaService.udienzaPerPresidenteByIdUdienza(
              await this.authService.getCurrentUser(),
              provv?.idUdienza,
            );
          datiUdienza.aula =
            udienzaPerPresidenteByIdUdienza?.aula?.descrizione ?? '';
          datiUdienza.sezione =
            udienzaPerPresidenteByIdUdienza?.sezione?.descrizione ?? '';
          datiUdienza.tipoUdienza =
            udienzaPerPresidenteByIdUdienza?.tipoUdienza?.descrizione ?? '';
          datiUdienza.dataUdienza =
            udienzaPerPresidenteByIdUdienza?.dataUdienza ?? new Date();
        } else {
          this.logger.warn(`Udienza non trovata. nrg:${provv?.idUdienza}`);
          throw new GenericErrorException(
            'Udienza non trovata.',
            CodeErrorEnumException.UDIENZA_NOT_FOUND,
          );
        }
      } catch (e) {
        this.logger.error(
          `Errore nella ricerca dell'udienza:${provv?.idUdienza}`,
          e,
        );
        throw e;
      }
      folderFascicolo = zip.folder(numero + '_' + anno)!;
      for (const ff of data) {
        const fascicoloInfo = new FascicoloInfo();
        fascicoloInfo.nrg = ricorso.nrg;
        fascicoloInfo.anno = anno ?? 0;
        fascicoloInfo.numero = numero ?? 0;
        fascicoloInfo.folder = numero + '_' + anno;
        const lastIndexOfp = ff.nomeFile.lastIndexOf('.');
        fascicoloInfo.filename = ff.nomeFile.substring(0, lastIndexOfp);
        fascicoloInfo.extesion = ff.nomeFile.substring(
          lastIndexOfp,
          ff.nomeFile.length,
        );
        fascicoloInfo.tipoProvv = provv.tipo;
        fascicoliIfos.push(fascicoloInfo);
        // Schedula download atto principale
        promises.push(
          this.downloadAtto(ff.idCategoria, ff.nomeFile, folderFascicolo),
        );
      }
      pilotInfos.push({
        fascicoliInfos: fascicoliIfos,
        udienzaInfo: datiUdienza,
      });
    }
    // Aspetta che tutti i download terminino
    await Promise.all(promises);
    zip.file(
      'indice.html',
      this.createHtmlIndexService.creaPilotPerCodaDeposito(pilotInfos),
    );
    const res = await zip.generateAsync({
      type: 'arraybuffer',
    });
    this.logger.log(
      `Archivio creato correttamete per la coda depositi. inputCodaDeposito:${JSON.stringify(
        inputCodaDeposito,
      )}`,
    );
    return new StreamableFile(Buffer.from(res), { type: 'application/zip' });
  }

  async downloadAtto(idCat: string, filename: string, folderFascicolo: JSZip) {
    this.logger.log(
      `Download dati dalla tabella provvedimenti. idCat:${idCat}`,
    );
    try {
      const data =
        await this.provvLavorazService.getProvvLavorazioneByIdCatUnlessDocx(
          idCat,
        );

      if (data) {
        folderFascicolo.file(`${filename}`, data?.content, {
          binary: true,
        });
      }
    } catch (err) {
      this.logger.error(`Errore nel download dell'atto :${idCat}`, err);
      throw new GenericErrorException(
        "Errore nel download dell'atto idCat:" + idCat,
        CodeErrorEnumException.ATTI_DOWNLOAD_ERROR,
      );
    }
  }

  async firmaCodeDiDeposito(
    currentUser: string,
    inputCodaDeposito: FirmaPresidenteInput,
  ) {
    this.logger.log(
      `Inizio la firma della coda di deposito del presidente. Lista dei provvedimenti che sto firmando: ${inputCodaDeposito?.daDepositare}`,
    );
    const listaDiProvvedimentiBloccatiPerLaFirma = new Array<string>();
    const listaProvvediementiLavorati = new Array<{
      idProvv: string;
      stato: string;
    }>();
    const penaleProvv = new Array<PenaleProvvedimentiEntity>();
    const codaDepositoList = await this.getCodaDeposito(currentUser);
    const codiceFiscaleMittente = await this.authService.getCurrentUser();
    const ufficioDB = await this.ufficiDBService.getFirstUfficio();
    Utils.checkUfficoDB(ufficioDB);
    const firmaProvvLavorazioneInput = new FirmaProvvLavorazioneInput();
    firmaProvvLavorazioneInput.firmato = true;
    firmaProvvLavorazioneInput.codiceUfficio = ufficioDB?.codiceUfficio;
    firmaProvvLavorazioneInput.credenzialiFirmaRemota =
      inputCodaDeposito.credenziali;
    firmaProvvLavorazioneInput.bustaMakerData = {
      codiceFiscaleMittente: codiceFiscaleMittente,
      codiceUfficioDestinatario: ufficioDB?.codiceUfficio,
      ruoloMittente: 'MAG',
    };

    if (
      codaDepositoList?.length &&
      inputCodaDeposito?.daDepositare?.length > 0
    ) {
      try {
        for (const codaDeposita of inputCodaDeposito.daDepositare) {
          try {
            listaProvvediementiLavorati.push({
              idProvv: codaDeposita,
              stato: 'LAVORATO',
            });
            if (
              !codaDepositoList.some(
                codaDep => codaDep.idProvv === codaDeposita,
              )
            ) {
              throw new GenericErrorException(
                'Provvedimento non presente nella coda deposito',
                CodeErrorEnumException.CODA_DEPOSITO_NOT_FOUND,
              );
            }
            const provvedimento =
              await this.provvedimentiService.provvedimentoById(codaDeposita);
            if (!provvedimento) {
              throw new GenericErrorException(
                'Provvedimento non trovato',
                CodeErrorEnumException.PROVV_NOT_FOUND,
              );
            }
            if (provvedimento.provvedimentoLock) {
              throw new GenericErrorException(
                'Provvedimento bloccato da un altro utente',
                CodeErrorEnumException.PROVV_LOCKED,
              );
            }
            Utils.checkAlreadyWorked(
              provvedimento,
              ProvvedimentiStatoEnum.INVIATO_IN_CANCEL_PRESIDENTE,
              this.logger,
            );

            Utils.checkAlreadyWorked(
              provvedimento,
              ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE,
              this.logger,
            );
            await this.provvedimentiService.lockProvvedimento(codaDeposita);
            listaDiProvvedimentiBloccatiPerLaFirma.push(codaDeposita);

            const allFileProvvedimento =
              await this.provvLavorazService.getAllFileProvvedimento(
                codaDeposita,
              );
            if (!allFileProvvedimento) {
              throw new GenericErrorException(
                'File da Firmare  non trovato',
                CodeErrorEnumException.FILE_NOT_FOUND,
              );
            }
            await this.firmaEDepositaTransational(
              provvedimento,
              firmaProvvLavorazioneInput,
              penaleProvv,
              allFileProvvedimento,
              currentUser,
            );
          } catch (e) {
            this.logger.warn(
              `Errore nella firma della coda di deposito per il idProvv:${codaDeposita}:`,
              e,
            );
            if (
              e instanceof GenericErrorException &&
              this.checkErrorCodeDeposito(e.errorCode)
            ) {
              listaProvvediementiLavorati.forEach(pro => {
                if (pro.idProvv === codaDeposita) {
                  const errorCode = e.errorCode;
                  pro.stato = CodeErrorEnumException[errorCode];
                }
              });
              continue;
            }
            throw e;
          }
        }
      } catch (e) {
        this.logger.error('Errore nella firma della coda di deposito:', e);
        throw e;
      } finally {
        this.logger.log(
          `Eseguo lo sblocco (unlockProvvedimenti) dei provvedimenti:${JSON.stringify(
            listaDiProvvedimentiBloccatiPerLaFirma,
          )}`,
        );
        await this.provvedimentiService.unlockProvvedimenti(
          listaDiProvvedimentiBloccatiPerLaFirma,
        );
      }
    } else {
      this.logger.warn(
        `Coda di Deposito vuota. idCodeDepositoList: ${inputCodaDeposito?.daDepositare}`,
      );
      throw new GenericErrorException(
        'Coda di Deposito vuota',
        CodeErrorEnumException.CODA_DEPOSITO_NOT_FOUND,
      );
    }
    this.logger.log(
      `Coda di Deposito firmata. idCodeDepositoList: ${inputCodaDeposito?.daDepositare}`,
    );
    this.logger.log(
      `Fine firmaCodeDiDeposito con risultato: ${JSON.stringify(
        listaProvvediementiLavorati,
      )}`,
    );
    return listaProvvediementiLavorati;
  }

  private checkErrorCodeDeposito(codeError: CodeErrorEnumException) {
    switch (codeError) {
      case CodeErrorEnumException.PROVV_GIA_FIRMATO_DEPOSITATO:
      case CodeErrorEnumException.FILE_NOT_FOUND:
      case CodeErrorEnumException.PROVV_LOCKED:
      case CodeErrorEnumException.PROVV_NOT_FOUND:
      case CodeErrorEnumException.CODA_DEPOSITO_NOT_FOUND:
      case CodeErrorEnumException.NRG_REALE_NOT_VALORIZED:
      case CodeErrorEnumException.RICORSO_NOT_FOUND:
      case CodeErrorEnumException.PDF_CORRUPTED_SIGN_ERROR:
        return true;
      default:
        return false;
    }
  }

  private async firmaEDepositaTransational(
    provvedimento: PenaleProvvedimentiEntity,
    firmaProvvLavorazioneInput: FirmaProvvLavorazioneInput,
    penaleProvv: PenaleProvvedimentiEntity[],
    allFileProvvedimento: PenaleProvvLavFileEntity[],
    currentUser: string,
  ) {
    await this.connection.manager.transaction(
      async transactionalEntityManager => {
        const provvLavorazione = new CreateProvvLavorazioneInput();
        provvLavorazione.nrg = provvedimento.nrg;
        provvLavorazione.origine = provvedimento.origine;
        provvLavorazione.idUdienza = provvedimento.idUdienza;
        provvLavorazione.argsProvvedimento =
          new ArgsDepositoProvvedimentoInput();
        provvLavorazione.argsProvvedimento.tipologiaProvvedimento =
          provvedimento.tipo;
        const ricorso = await this.ricorsoService.ricorsoFindByNrg(
          provvedimento.nrg,
        );
        if (!ricorso?.nrgReale) {
          this.logger.warn(
            `Ricorso o nrgRelae non trovato. nrg:${provvedimento?.nrg}`,
          );
          throw new GenericErrorException(
            'Ricorso non trovato o nrgReale non valorizzato',
            ricorso
              ? CodeErrorEnumException.NRG_REALE_NOT_VALORIZED
              : CodeErrorEnumException.RICORSO_NOT_FOUND,
          );
        }
        const [anno, numero] = Utils.calcoloNumeroFascicolo(
          ricorso.nrgReale.toString(),
        );
        if (!anno || !numero) {
          this.logger.error(
            `Anno o numero fascicolo non calcolato. anno::${anno}, numero: ${numero}`,
          );
          throw new GenericErrorException(
            'Anno o numero fascicolo non calcolato.',
            CodeErrorEnumException.CALCOLO_NUMERO_FASCICOLO_ERROR,
          );
        }
        firmaProvvLavorazioneInput.nrg = provvLavorazione.nrg;
        penaleProvv.push(
          await this.firmaEDepositaPresidente(
            allFileProvvedimento,
            firmaProvvLavorazioneInput,
            anno,
            numero,
            provvLavorazione.argsProvvedimento.tipologiaProvvedimento,
            provvedimento,
            currentUser,
            provvedimento.idProvvedimento,
          ),
        );
      },
    );
  }
  async getProvvIsEstensoreIsPresidente(idProvv: string) {
    const provv = await this.getProvvLavorazioneByIdProvv(idProvv);
    if (!provv) {
      throw new GenericErrorException(
        'errore nella ricerca del provvedimento in lavorazione',
        CodeErrorEnumException.PROVV_NOT_FOUND,
      );
    }
    const isPresidente = await this.authService.isPresidenteByNrgAndIdUdienza(
      provv?.nrg,
      provv?.idUdienza,
    );
    const isEstensore = await this.authService.isEstensoreforNrgAndIdUdienza(
      provv?.nrg,
      provv?.idUdienza,
    );
    return { provv, isEstensore, isPresidente };
  }
  /**
   * Crea una copia del provvedimento per il presidente
   * @param idProvv
   * @private
   */
  async creaCopyDelProvvedimento(
    provv: PenaleProvvedimentiEntity,
    isPresidente: boolean,
    isEstensore: boolean,
    entityManager?: EntityManager,
    isVerificato?: boolean,
  ): Promise<PenaleProvvedimentiEntity | null> {
    this.logger.log(
      `Inizio la Creazione della copia del provvedimento. idProvv: ${provv.idProvvedimento}`,
    );

    //copia del provvedimento per il presidente
    const clonaProvv = (({
      idProvvedimento,
      provvedimentoLock,
      modificaPresidente,
      ...o
    }) => o)(provv);
    const copyProvv = new PenaleProvvedimentiEntity(clonaProvv);
    copyProvv.tipo =
      isVerificato || (isPresidente && isEstensore)
        ? Utils.getTipoProvvPerDuplicatePerPresidente(provv.tipo)
        : Utils.getTipoProvvPerDuplicatePerEstensore(provv.tipo);
    copyProvv.idAutore = await this.authService.getCurrentId();
    copyProvv.dataUltimaModifica = new Date();
    const clonecopyProvv = (({
      fkIdCat,
      provvedimentoLock,
      modificaPresidente,
      ...o
    }) => o)(copyProvv);
    const newVar = await this.provvedimentiService.saveCopyProvvedimento(
      clonecopyProvv as PenaleProvvedimentiEntity,
      entityManager,
    );
    if (newVar) {
      this.logger.log(
        `Fine la Creazione della copia del provvedimento. idProvv: ${provv.idProvvedimento}`,
      );
      return newVar;
    }
    this.logger.log(
      `Id provvedimento non trovato. idProvv: ${provv.idProvvedimento}`,
    );
    throw new GenericErrorException(
      'Provvedimento non trovato',
      CodeErrorEnumException.PROVV_NOT_FOUND,
    );
  }

  async downloadAndDuplicateFileSigned(
    idAtto: string,
    newIdprovv: string,
    file: PenaleProvvLavFileEntity,
    entityManager: EntityManager,
  ) {
    this.logger.log(
      `Inizio del Download del provvedimento firmato da gl. idProvv: ${newIdprovv}, idAtto: ${idAtto}`,
    );
    const ufficioDB = await this.ufficiDBService.getFirstUfficio();
    Utils.checkUfficoDB(ufficioDB);
    //TODO CODICE DA VERIFCIARE
    const urlDeposito = `${this.configService.get(
      'app.depositiUrl',
    )}/atti/download-atto-informatico/${idAtto}?originale=true&codiceUfficio=${
      ufficioDB?.codiceUfficio
    }&registro=CASSPENALE`;
    this.logger.debug(`call servizi depositi:${urlDeposito}`);
    const pdfNonOscuato = await axios
      .get(urlDeposito, { responseType: 'arraybuffer' })
      .then(response => {
        if (response.status === 200) {
          this.logger.log(
            `Fine del Download del provvedimento firmato da gl. idProvv: ${newIdprovv}, idAtto: ${idAtto}`,
          );
          return response.data;
        } else {
          this.logger.error(
            `Errore nel download dei file firmati. idAtto:${idAtto}, idProvv: ${newIdprovv},  ${response.status}: ${response.statusText}`,
          );
          throw new ServiziDepositoException(
            'depError:' + response?.data || '',
          );
        }
      })
      .catch(err => {
        this.logger.error(
          `Errore nel download dei file firmati: idAtto:${idAtto}, idProvv: ${newIdprovv}, url: ${urlDeposito}`,
          err,
        );
        if (err.response != undefined)
          throw new ServiziDepositoException(
            "Errore nel contattare il servizio 'servizio-depositi': " +
              err.response.data,
          );
        else {
          throw new ServiziDepositoException('noServiceError');
        }
      });

    return await this.duplicaPdfFirme(
      Buffer.from(pdfNonOscuato),
      newIdprovv,
      file,
      entityManager,
    );
  }

  async duplicaProvvedimentoFile(
    file: PenaleProvvLavFileEntity[] | null,
    newIdprovv: string,
    entityManager?: EntityManager,
  ) {
    this.logger.log(
      `Inizio inserimento dei file firmati per firma del presidente. idProvv: ${newIdprovv}`,
    );
    if (file && file.length > 0) {
      const provvedimentiDaLavorare = new Array<PenaleProvvLavFileEntity>();
      for (const penaleProvvLavFileEntity of file) {
        const clonepenaleProvvLavFileEntity = (({ idCategoria, ...o }) => o)(
          penaleProvvLavFileEntity,
        );
        const provvLav = new PenaleProvvLavFileEntity(
          clonepenaleProvvLavFileEntity as PenaleProvvLavFileEntity,
        );
        provvLav.idProvvedimento = newIdprovv;
        const promise =
          await this.provvLavorazService.saveCopyProvvedimentiLavorazione(
            provvLav,
            entityManager,
          );
        if (promise) {
          provvedimentiDaLavorare.push(promise);
        }
      }
      this.logger.log(
        `Fine inserimento dei file firmati per firma del presidente. idProvv: ${newIdprovv}`,
      );
      return provvedimentiDaLavorare;
    }
    this.logger.warn(
      `Lista dei file da inserire nel database vuota. idProvv: ${newIdprovv}`,
    );
    return null;
  }

  async duplicaPdfFirme(
    filePdf: any,
    newIdprovv: string,
    penaleProvvLavFileEntity: PenaleProvvLavFileEntity,
    entityManager: EntityManager,
  ) {
    this.logger.log(
      `Inizio della duplicazioe dei file da inserire nel database. idProvv: ${newIdprovv}`,
    );
    const provvedimentiDaLavorare = new Array<PenaleProvvLavFileEntity>();
    const clonepenaleProvvLavFileEntity = (({ idCategoria, ...o }) => o)(
      penaleProvvLavFileEntity,
    );
    const provvLav = new PenaleProvvLavFileEntity(
      clonepenaleProvvLavFileEntity as PenaleProvvLavFileEntity,
    );
    provvLav.content = filePdf;
    provvLav.idProvvedimento = newIdprovv;
    const promise =
      await this.provvLavorazService.saveCopyProvvedimentiLavorazione(
        provvLav,
        entityManager,
      );
    if (promise) {
      provvedimentiDaLavorare.push(promise);
    }
    this.logger.log(
      `Fine della duplicazioe dei file da inserire nel database. idProvv: ${newIdprovv}`,
    );
    return provvedimentiDaLavorare;
  }

  private async creaNotificaPerIlRelatore(
    provv: PenaleProvvedimentiEntity,
    noteString: string | null,
    modificaPresidente: boolean,
  ) {
    this.logger.log(
      `Inizio della creazione delle notifica. idProvv: ${provv.idProvvedimento}`,
    );
    let idAutore = provv.idAutore;
    if (modificaPresidente) {
      //restituisce il fascicolo figlio dell'estesore
      const provvFiglioEstensore =
        await this.getProvvFiglioEstensoreForRichiestaModificaEModifica(provv);
      if (provvFiglioEstensore?.idAutore) {
        idAutore = provvFiglioEstensore?.idAutore;
      }
    }

    const notifica = new CreateNotificheInput();
    notifica.nrg = provv.nrg;
    notifica.tipo = provv.modificaPresidente
      ? NotificaTipoEnum.MINUTA_MODIFICATA_PRESIDENTE
      : NotificaTipoEnum.RICHIESTA_MODIFICA;
    notifica.idUdien = provv.idUdienza;
    notifica.descrizione =
      (provv.modificaPresidente
        ? 'Minuta modificata dal presidente: '
        : 'Richiesta di modifica: ') + noteString || '';
    await this.notificheService.createNotifiche(notifica, idAutore);
    this.logger.log(
      `Fine della creazione delle notifica. idProvv: ${provv.idProvvedimento}`,
    );
  }

  private async insertChangeStatusProvv(
    provv: PenaleProvvedimentiEntity,
    idAutore: number,
    tipoModifica?: PresidenteTipoModificaEnum | undefined,
    entityManager?: EntityManager,
    prevStatus?: ProvvedimentiStatoEnum,
  ) {
    this.logger.log(
      `Inizio dell'inserimento del cambiamento di stato del provvedimento. idProvv: ${provv.idProvvedimento}`,
    );

    const stato =
      tipoModifica === PresidenteTipoModificaEnum.MINUTA_MODIFICATA_PRESIDENTE
        ? ProvvedimentiStatoEnum.MINUTA_MODIFICATA_PRESIDENTE
        : ProvvedimentiStatoEnum.MINUTA_DA_MODIFICARE;

    const changeStatusValue = new CreateProvvedimentiChangeStatusInput();
    changeStatusValue.idProvvedimento = provv.idProvvedimento;
    changeStatusValue.stato = stato;
    changeStatusValue.idAutore = idAutore;
    changeStatusValue.prevStato = prevStatus ?? provv.stato;

    await this.changeStatusService.createProvvedimentoChangeStatus(
      changeStatusValue,
      entityManager,
    );
    this.logger.log(
      `Fine dell'inserimento del cambiamento di stato del provvedimento. idProvv: ${provv.idProvvedimento}`,
    );
  }

  private async creaNotaSulProvvedimento(
    provv: PenaleProvvedimentiEntity,
    noteString: string,
    idAutore: number,
    entityManager?: EntityManager,
  ) {
    this.logger.log(
      `Inizio creazione di nota sul provvedimento. idProvv: ${provv.idProvvedimento}`,
    );
    const note = new CreateProvvedimentiNoteInput();
    note.idProvvedimento = provv.idProvvedimento;
    note.idAutore = idAutore;
    note.note = noteString || '';
    await this.provvedimentoNoteService.createProvvedimentoNote(
      note,
      entityManager,
    );
    this.logger.log(
      `Fine creazione di nota sul provvedimento. idProvv: ${provv.idProvvedimento}`,
    );
  }

  /**
   * Firma una coda deposito sia del presidente che dell'estensore riutilizzata dopo aver introdotto la coda deposito dell'estensore.
   * @param listaDeiPdfDuplicati
   * @param firmaProvvLavorazioneInput
   * @param anno
   * @param numero
   * @param tipologiaProvvedimento
   * @param provvCopiato
   * @param currentUser
   * @param idProvvCopiato
   * @private
   */

  private async firmaEDepositaPresidente(
    listaDeiPdfDuplicati: PenaleProvvLavFileEntity[],
    firmaProvvLavorazioneInput: FirmaProvvLavorazioneInput,
    anno: number,
    numero: number,
    tipologiaProvvedimento: ProvvedimentiTipoEnum,
    provvCopiato: PenaleProvvedimentiEntity,
    currentUser: string,
    idProvvCopiato: string,
  ) {
    this.logger.log(
      `Inizio firma e deposita del presidente  di nota sul provvedimento. idProvv: ${idProvvCopiato}, nrg: ${provvCopiato.nrg}`,
    );
    const file = listaDeiPdfDuplicati.filter(file => file.tipoFile == 'pdf');

    Utils.checkAlreadyWorked(
      provvCopiato,
      ProvvedimentiStatoEnum.INVIATO_IN_CANCEL_PRESIDENTE,
      this.logger,
    );

    Utils.checkAlreadyWorked(
      provvCopiato,
      ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE,
      this.logger,
    );
    firmaProvvLavorazioneInput.generazioneDatiAtto = {
      tipoProvvedimento: tipologiaProvvedimento,
      annoFascicolo: anno,
      numeroFascicolo: numero,
      allegatoOscurato: file?.length > 1,
    };
    firmaProvvLavorazioneInput.idProvvedimento = provvCopiato.idProvvedimento;
    firmaProvvLavorazioneInput.tipologiaProvvedimento = provvCopiato.tipo;
    const fileXml = listaDeiPdfDuplicati.find(file => file.tipoFile == 'xml');
    if (!file || !fileXml) {
      this.logger.error(
        `Errore nella ricerca del provvedimento in lavorazione. idProvv: ${idProvvCopiato}, nrg: ${provvCopiato.nrg}`,
      );
      throw new GenericErrorException(
        'Nella ricerca del provvedimento in lavorazione',
        CodeErrorEnumException.FILE_NOT_FOUND,
      );
    }
    firmaProvvLavorazioneInput.tipologiaProvvedimento = provvCopiato.tipo;
    firmaProvvLavorazioneInput.idProvvedimento = provvCopiato.idProvvedimento;
    const idCat = await this.provvLavorazService.firmaEDeposita(
      firmaProvvLavorazioneInput,
      file,
      fileXml,
    );
    if (!Number(idCat)) {
      throw new FirmaEDepositaException(
        'Errore nella firma e deposito. idCat null',
      );
    }
    const num = Number(idCat);
    const idAutore = await this.authService.getCurrentId();
    const changeStatusValue = new CreateProvvedimentiChangeStatusInput();
    changeStatusValue.idProvvedimento = provvCopiato.idProvvedimento;

    const statoNew = (await this.authService.isEstensoreforNrg(
      provvCopiato.nrg,
    ))
      ? ProvvedimentiStatoEnum.INVIATO_IN_CANCELLERIA_RELATORE
      : ProvvedimentiStatoEnum.INVIATO_IN_CANCEL_PRESIDENTE;

    changeStatusValue.stato = statoNew;
    changeStatusValue.idAutore = idAutore;
    changeStatusValue.prevStato = provvCopiato.stato;
    provvCopiato.stato = statoNew;
    provvCopiato.dataUltimaModifica = new Date();

    this.logger.debug(
      'Metodo firma e deposita del presidente  e inserimento nel change status.',
    );
    await this.changeStatusService.createProvvedimentoChangeStatus(
      changeStatusValue,
    );
    provvCopiato.dataDeposito = new Date();
    const provvWithIDCat = await this.provvLavorazService.setIdCat(
      idCat,
      new PenaleProvvedimentiEntity({ ...provvCopiato }),
    );
    await this.deleteProvvedimento(idProvvCopiato, currentUser);
    this.logger.log(
      `Fine firma e deposita del presidente  di nota sul provvedimento. idProvv: ${idProvvCopiato}, nrg: ${provvCopiato.nrg}`,
    );
    return provvWithIDCat;
  }

  /**
   * Crea una copia dei file per il presidente
   * @param idProvv
   * @param idCat
   * @param newIdprovv
   * @param provvLavorazione
   * @private
   */
  private async creaCopyDelProvvedimentoLavorazioneFileAndRigeneraDAtiAtto(
    idProvv: string,
    idCat: number,
    newIdprovv: string,
    provvLavorazione: CreateProvvLavorazioneInput,
    entityManager: EntityManager,
  ) {
    const provvedimentiDaLavorare = new Array<PenaleProvvLavFileEntity>();
    this.logger.log(
      `Inizio copia dei file del provvedimento e rigenerazione del dato atto. idprovv: ${idProvv}, newIdProvv: ${newIdprovv}`,
    );
    const idCatQuery: any[] = await this.connection.query(
      "SELECT IDCAT, TIPOGL FROM CSPBACKEND_ATTI where idcatbusta = :idCat AND (TIPOGL = 'ATTO' OR TIPOGL = 'SM') ",
      [idCat],
    );
    this.logger.debug('Risultato dei file firmati:', idCatQuery);
    let idAttoNonOscuato = undefined;
    let idAttoOscuato = undefined;
    if (idCatQuery.length > 0) {
      //abbiamo solo il file oscuarato
      idAttoNonOscuato = idCatQuery.filter(icats => icats.TIPOGL == 'ATTO')[0]
        .IDCAT;
      if (idCatQuery.length > 1) {
        //Significia che abbiamo anche il file oscurato
        idAttoOscuato = idCatQuery.filter(icats => icats.TIPOGL == 'SM')[0]
          .IDCAT;
      }
    }
    const file = await this.provvLavorazService.getFileProvvedimento(
      idProvv,
      'pdf',
    );

    const fileNonOscuato = file.find(osc => !osc.oscurato);
    const fileOscuato = file.find(osc => osc.oscurato);
    this.logger.log(
      `file rigenerati id provvedimenti. idProvvNonOscurato:${fileNonOscuato?.idProvvedimento}, idProvvOscurato:${fileOscuato?.idProvvedimento}`,
    );
    if (idAttoNonOscuato && fileNonOscuato) {
      const penaleProvvLavFileEntities =
        await this.downloadAndDuplicateFileSigned(
          idAttoNonOscuato,
          newIdprovv,
          fileNonOscuato,
          entityManager,
        );
      provvedimentiDaLavorare.push(...penaleProvvLavFileEntities);
    }
    if (idAttoOscuato && fileOscuato) {
      this.logger.log('Inserisco il file oscurato firmato nel databse');
      const penaleProvvLavFileEntities =
        await this.downloadAndDuplicateFileSigned(
          idAttoOscuato,
          newIdprovv,
          fileOscuato,
          entityManager,
        );
      this.logger.debug(
        "Forzo allegato oscurato a true perchè esiste l'allegato oscurato nei file firmati",
      );
      provvLavorazione.allegatoOscurato = true;
      provvedimentiDaLavorare.push(...penaleProvvLavFileEntities);
    }
    this.logger.log(' scaricati i file firmati dalla cancelleria');
    this.logger.log(
      ` rigenero il datoAtto.xml con parametri:${JSON.stringify(
        provvLavorazione,
      )}`,
    );
    // generaDatiAttoPerProvvedimentoCopiato
    const bufferDatiAtto = await this.provvedimentiService.generaDatiAtto(
      provvLavorazione,
    );
    if (bufferDatiAtto) {
      await this.provvLavorazService.saveFileDatiAtto(
        bufferDatiAtto,
        newIdprovv,
        entityManager,
      );
    } else {
      this.logger.error(
        `Errore nella generazione dei dati atto per provv:${idProvv}`,
      );
      throw new DuplicaProvvedimentoException(
        'Errore nella generazione dei dati atto',
      );
    }
    this.logger.log(
      `Fine copia dei file del provvedimento e rigenerazione del dato atto. idprovv: ${idProvv}, newIdProvv: ${newIdprovv}`,
    );
    return provvedimentiDaLavorare;
  }
}
